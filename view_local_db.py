import sqlite3

def view_database():
    # 连接到 SQLite 数据库
    conn = sqlite3.connect('data/sync_data.db')
    cursor = conn.cursor()

    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()

    if not tables:
        print("数据库中没有表。")
        return

    print("数据库中的表：")
    for table in tables:
        table_name = table[0]
        print(f"\n表名: {table_name}")
        
        # 获取表的前 10 条记录
        cursor.execute(f"SELECT * FROM \"{table_name}\" LIMIT 10")
        rows = cursor.fetchall()
        
        if not rows:
            print("表中没有数据。")
            continue
        
        # 打印列名
        cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        print("列名:", ", ".join(column_names))
        
        # 打印数据
        print("前 10 条记录：")
        for row in rows:
            for i, value in enumerate(row):
                print(f"{column_names[i]}: {value}")
            print("----")
    
    # 关闭连接
    conn.close()

if __name__ == "__main__":
    view_database()