import pathlib
import os
_real_exists = pathlib.Path.exists
def safe_exists(self):
    try:
        return _real_exists(self)
    except OSError:
        return False
pathlib.Path.exists = safe_exists
import pymongo
import sqlite3
import requests
from config import FIELDS, FIELD_DEF

# MongoDB 连接配置
SRC_MONGO_URI = "mongodb+srv://readonly:<EMAIL>/Cluster0?retryWrites=true&w=majority"
SRC_MONGO_DB = "sehuatang"


def main():
    # 连接MongoDB
    src_client = pymongo.MongoClient(SRC_MONGO_URI)
    src_db = src_client[SRC_MONGO_DB]
    collections = src_db.list_collection_names()
    
    # 确保 data 目录存在
    os.makedirs('data', exist_ok=True)
    
    # 初始化 SQLite 数据库
    sqlite_conn = sqlite3.connect('data/sync_data.db')
    sqlite_cursor = sqlite_conn.cursor()



    for collection_name in collections:
        if collection_name == 'old_backup':
            continue
        src_col = src_db[collection_name]
        print(f"同步集合: {collection_name}")
        # 创建 SQLite 表
        sqlite_create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS "{collection_name}" (
            {FIELD_DEF}
        );
        """
        sqlite_cursor.execute(sqlite_create_table_sql)
        sqlite_conn.commit()
        # 增量同步：查找本地最大post_time
        sqlite_cursor.execute(f"SELECT MAX(post_time) FROM \"{collection_name}\"")
        latest_post_time = sqlite_cursor.fetchone()[0]
        query = {}
        if latest_post_time:
            query = {"post_time": {"$gt": latest_post_time}}
        docs = list(src_col.find(query).limit(10))
        BATCH_SIZE = 200
        total = len(docs)
        # 获取当前最大 auto_id
        sqlite_cursor.execute(f"SELECT MAX(auto_id) FROM \"{collection_name}\"")
        max_auto_id = sqlite_cursor.fetchone()[0] or 0
        for i in range(0, total, BATCH_SIZE):
            batch = docs[i:i+BATCH_SIZE]
            values_list = []
            for doc in batch:
                max_auto_id += 1
                doc["auto_id"] = max_auto_id
                values = [str(doc.get(f, '')) for f in FIELDS]
                values_list.append(values)
            placeholders = ','.join(['?'] * len(FIELDS))
            # 同步数据到 SQLite
            sqlite_insert_sql = f"INSERT OR IGNORE INTO \"{collection_name}\" ({','.join(FIELDS)}) VALUES ({placeholders})"
            sqlite_cursor.executemany(sqlite_insert_sql, values_list)
            sqlite_conn.commit()
            
            print(f"集合 {collection_name}: 已同步 {min(i+BATCH_SIZE, total)}/{total} 条数据...")
        print(f"集合 {collection_name}: 同步完成，共 {total} 条数据。")

    sqlite_cursor.close()
    sqlite_conn.close()
    src_client.close()
    print("全部同步完成！")



if __name__ == "__main__":
    main()