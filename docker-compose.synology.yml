# 群晖NAS专用Docker Compose配置
# 使用方法：docker-compose -f docker-compose.synology.yml up -d

version: '3.8'

services:
  hunsu-web:
    build: .
    container_name: hunsu-web
    ports:
      - "27020:27020"  # 映射到群晖的27020端口
    volumes:
      # 数据持久化到群晖共享文件夹
      - /volume1/docker/hunsu-data:/app/data
      # 配置文件挂载（可选，如需自定义配置）
      - ./config.py:/app/config.py:ro
    environment:
      - DATA_DIR=/app/data
      - PYTHONUNBUFFERED=1
      # 群晖时区设置
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - hunsu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:27020/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.centurylinklabs.watchtower.enable=true"  # 支持Watchtower自动更新

  hunsu-sync:
    build: .
    container_name: hunsu-sync
    command: python check_mongo_conn.py
    volumes:
      # 共享数据目录
      - /volume1/docker/hunsu-data:/app/data
      - ./config.py:/app/config.py:ro
    environment:
      - DATA_DIR=/app/data
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - hunsu-network
    depends_on:
      - hunsu-web
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

networks:
  hunsu-network:
    driver: bridge

# 群晖部署说明：
# 1. 在群晖File Station中创建目录：/docker/hunsu-data
# 2. 将项目文件上传到群晖
# 3. SSH登录群晖，进入项目目录
# 4. 运行：docker-compose -f docker-compose.synology.yml up -d
# 5. 访问：http://群晖IP:27020
