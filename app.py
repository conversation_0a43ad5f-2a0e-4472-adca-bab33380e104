# ====== 统一导入区 ======
from flask import Flask, render_template, request, jsonify, send_from_directory
import datetime
import os
import sqlite3
import requests
import re
import ast

from config import RUN_HOST, RUN_PORT, EMBY_SERVER_URL, EMBY_API_KEY, EMBY_LIBRARY_ID, QUERY_DATABASE_PATH, LADY_DATABASE_PATH, ENABLE_115_PUSH, COOKIE_115, TARGET_DIR_115, HTTP_PROXY, HTTPS_PROXY
import pathlib
from functools import wraps

# ====== 工具函数区 ======
_real_exists = pathlib.Path.exists
def safe_exists(self):
    try:
        return _real_exists(self)
    except OSError:
        return False
pathlib.Path.exists = safe_exists

# ====== Flask应用定义 ======
app = Flask(__name__)
print(f"QUERY_DATABASE_PATH: {QUERY_DATABASE_PATH}")

# 配置日志
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
handler.setLevel(logging.DEBUG)
logger.addHandler(handler)

# 确保路径使用原始字符串
QUERY_DATABASE_PATH = QUERY_DATABASE_PATH.replace("\\", "/")


# ====== 数据库连接装饰器 ======

def with_sqlite_connection(func):
    """用于 sync_data.db 的连接装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        conn = None
        cursor = None
        try:
            conn = sqlite3.connect(QUERY_DATABASE_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            result = func(cursor, *args, **kwargs)
            conn.commit()
            return result
        except Exception as e:
            logger.error(f'数据库操作失败: {str(e)}')
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    return wrapper

def with_lady_db_connection(func):
    """用于 lady.db 的连接装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        conn = None
        cursor = None
        try:
            conn = sqlite3.connect(LADY_DATABASE_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            result = func(cursor, *args, **kwargs)
            conn.commit()
            return result
        except Exception as e:
            logger.error(f'Lady数据库操作失败: {str(e)}')
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    return wrapper

# ====== 115 推送相关（受配置开关控制）======

def _parse_cookie(cookie_str: str):
    cookie_dict = {}
    if not cookie_str:
        return cookie_dict
    for item in str(cookie_str).split(';'):
        item = item.strip()
        if '=' in item:
            k, v = item.split('=', 1)
            cookie_dict[k] = v
    return cookie_dict


def _get_115_cid_by_path(cookies: dict, target: str) -> str | None:
    try:
        # 如果直接是数字，认为已经是 cid
        if target and str(target).isdigit():
            return str(target)
        # 需要将路径解析成 cid
        path = target if target and target.startswith('/') else f'/{target or ""}'
        url = 'https://webapi.115.com/files'
        params = {'ct': 'folder', 'ac': 'get_id_by_path', 'path': path}
        headers = {
            'Referer': 'https://115.com/?ct=lixian',
            'User-Agent': 'Mozilla/5.0'
        }
        proxies = {
            "http": HTTP_PROXY,
            "https": HTTPS_PROXY
        } if HTTP_PROXY else None
        r = requests.get(url, params=params, cookies=cookies, headers=headers, timeout=10, proxies=proxies)
        if r.status_code != 200:
            return None
        data = r.json() if r.content else {}
        # 返回示例：{"state":true,"id":"1234567890"}
        cid = str(data.get('id') or data.get('cid') or '').strip()
        return cid or None
    except Exception:
        return None


def _push_to_115_single(magnet_link: str) -> bool:
    try:
        if not ENABLE_115_PUSH:
            return False
        if not magnet_link:
            return False
        cookies = _parse_cookie(COOKIE_115)
        if not cookies or not TARGET_DIR_115:
            return False
        # 解析目录为 cid
        cid = _get_115_cid_by_path(cookies, TARGET_DIR_115)
        if not cid:
            # 解析失败时仍尝试用 savepath 兼容
            cid = None
        add_url = 'https://115.com/web/lixian/?ct=lixian&ac=add_task_url'
        data = {'url': magnet_link}
        if cid:
            data['wp_path_id'] = cid
        # 兼容旧参数：仅当配置为路径时才传 savepath，避免把 CID 当作目录名
        if isinstance(TARGET_DIR_115, str) and TARGET_DIR_115.strip().startswith('/'):
            data['savepath'] = TARGET_DIR_115
        headers = {
            'Referer': 'https://115.com/?ct=lixian',
            'User-Agent': 'Mozilla/5.0'
        }
        r = requests.post(add_url, cookies=cookies, headers=headers, data=data, timeout=12)
        if r.status_code != 200:
            return False
        try:
            js = r.json()
            # 常见返回：{"state":true,...} 或 errno=0
            if isinstance(js, dict):
                if js.get('state') is True:
                    return True
                if js.get('errno') in (0, '0') or js.get('errcode') in (0, '0'):
                    return True
        except Exception:
            pass
        return True  # 200 视作已提交任务
    except Exception:
        return False


@app.route('/api/push115', methods=['POST'])
def api_push115():
    if not ENABLE_115_PUSH:
        return jsonify({'ok': False, 'message': '115 推送功能已关闭，请在 config.py 启用 ENABLE_115_PUSH'}), 400
    payload = request.get_json(silent=True) or {}
    magnets = payload.get('magnets') or []
    if not isinstance(magnets, list) or not magnets:
        return jsonify({'ok': False, 'message': '未收到磁力链接'}), 400
    pushed = 0
    for m in magnets:
        if isinstance(m, str) and m.startswith('magnet:') and _push_to_115_single(m):
            pushed += 1
    failed = max(0, len(magnets) - pushed)
    return jsonify({'ok': True, 'pushed': pushed, 'failed': failed})

# ====== API接口 ======
@app.route('/api/query_by_category')
def api_query_by_category():
    category = request.args.get('category', '').strip()
    date = request.args.get('date', '').strip()
    if not category or not date:
        return jsonify({'error': '分类和日期不能为空'}), 400

    try:
        with sqlite3.connect(QUERY_DATABASE_PATH) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (category,))
            if not cursor.fetchone():
                return jsonify({'error': f'分类 {category} 不存在'}), 404
            # 查询数据
            cursor.execute(
                "SELECT * FROM \"{category}\" WHERE DATE(date) = ?".format(category=category),
                (date,)
            )
            results = [dict(row) for row in cursor.fetchall()]
            return jsonify({'data': results})
    except sqlite3.Error as e:
        logger.error(f'数据库查询失败: {str(e)}')
        return jsonify({'error': f'数据库查询失败: {str(e)}'}), 500
    except Exception as e:
        logger.error(f'服务器内部错误: {str(e)}')
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@app.route('/api/check_number')
def api_check_number():
    # 兼容 keyword 参数（有些表单可能用 keyword 作为番号输入名）
    number = request.args.get('number', '').strip() or request.args.get('keyword', '').strip()
    if not number:
        return jsonify({'exist': False, 'magnet': ''})

    try:
        with sqlite3.connect(QUERY_DATABASE_PATH) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            exist = False
            magnet = ''

            for table in tables:
                cursor.execute(f"SELECT magnet FROM \"{table}\" WHERE number=? LIMIT 1", (number,))
                row = cursor.fetchone()
                if row and row[0]:
                    exist = True
                    magnet = row[0]
                    break  # 找到后立即终止查询

            return jsonify({'exist': exist, 'magnet': magnet})
    except Exception as e:
        logger.error(f'检查番号失败: {str(e)}')
        return jsonify({'exist': False, 'magnet': '', 'error': str(e)})

@app.route('/api/check_emby_number')
def api_check_emby_number():
    """
    根据番号在 Emby 中模糊查询是否已入库。
    - 多形态搜索：原样/大写/去连字符/空格分隔等
    - 强匹配：对返回条目名称做“去非字母数字”的规整后比对，确保 MIDA-319 可命中
    - 兜底：Items 无果时尝试 Search/Hints
    """
    number = request.args.get('number', '').strip()
    if not number:
        return jsonify({'exist': False})
    try:
        base_items = EMBY_SERVER_URL.rstrip('/') + '/emby/Items'
        base_hints = EMBY_SERVER_URL.rstrip('/') + '/emby/Search/Hints'

        def norm(s: str) -> str:
            return re.sub(r'[^0-9A-Za-z]+', '', s or '').upper()

        target = norm(number)
        num_u = number.upper()
        terms = {number, num_u, num_u.replace('-', ' '), num_u.replace('-', '')}
        if '-' in num_u:
            left, right = num_u.split('-', 1)
            terms.add(f"{left} {right}")
            terms.add(f"{left}{right}")

        common_params = {'Recursive': 'true', 'Limit': 25, 'IncludeItemTypes': 'Movie,Video,Series,Episode'}
        if EMBY_LIBRARY_ID:
            common_params['ParentId'] = EMBY_LIBRARY_ID

        # 优先尝试 Items
        for term in terms:
            params = {**common_params, 'api_key': EMBY_API_KEY, 'SearchTerm': term}
            proxies = {
                "http": HTTP_PROXY,
                "https": HTTPS_PROXY
            } if HTTP_PROXY else None
            r = requests.get(base_items, params=params, timeout=8, proxies=proxies)
            if r.status_code == 200 and r.content:
                data = r.json()
                items = data.get('Items') or []
                for it in items:
                    name = it.get('Name') or it.get('OriginalTitle') or it.get('SeriesName') or ''
                    if target and target in norm(name):
                        return jsonify({'exist': True})
                total = data.get('TotalRecordCount')
                if isinstance(total, int) and total > 0:
                    return jsonify({'exist': True})

        # 兜底：Search/Hints
        for term in terms:
            params = {**common_params, 'api_key': EMBY_API_KEY, 'SearchTerm': term}
            proxies = {
                "http": HTTP_PROXY,
                "https": HTTPS_PROXY
            } if HTTP_PROXY else None
            r = requests.get(base_hints, params=params, timeout=8, proxies=proxies)
            if r.status_code == 200 and r.content:
                data = r.json()
                hints = data.get('SearchHints') or []
                for h in hints:
                    name = h.get('Name') or ''
                    if target and target in norm(name):
                        return jsonify({'exist': True})

        return jsonify({'exist': False})
    except Exception:
        return jsonify({'exist': False})

COLLECTION_LABELS = {
    "asia_codeless_originate": "亚洲无码",
    "anime_originate": "动漫",
    "domestic_original": "国内原创",
    "EU_US_no_mosaic": "欧美无码",
    "4k_video": "4K视频",
    "hd_chinese_subtitles": "高清中字",
    "asia_mosaic_originate": "亚洲有码",
    "three_levels_photo": "三级Photo",
    "vegan_with_mosaic": "素食有码",
    "vr_video": "VR视频"
}


def get_collections(cursor):
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    rows = cursor.fetchall()
    # 统一处理为列表格式
    all_tables = set()
    for row in rows:
        table_name = row[0] if len(row) > 0 else ''
        if table_name:
            all_tables.add(table_name)
    return [k for k in COLLECTION_LABELS if k in all_tables]


def get_today_stats(cursor, date_str, selected_collections, date_end=None):
    result = []
    total = 0
    for name in selected_collections:
        try:
            if date_end and date_end != date_str:
                cursor.execute(f"SELECT * FROM `{name}` WHERE `date` >= ? AND `date` <= ?", (date_str, date_end))
            else:
                cursor.execute(f"SELECT * FROM `{name}` WHERE `date` = ?", (date_str,))
            docs = cursor.fetchall()
        except sqlite3.Error as e:
            logger.error(f'查询表 {name} 失败: {str(e)}')
            docs = []
        count = len(docs)
        details = []
        for doc in docs:
            img_val = doc.get("img", "")
            if isinstance(img_val, list):
                img_url = img_val[0] if img_val else ""
            elif isinstance(img_val, str) and img_val.strip().startswith("["):
                try:
                    img_list = ast.literal_eval(img_val)
                    img_url = img_list[0] if isinstance(img_list, list) and img_list else ""
                except Exception:
                    img_url = img_val
            else:
                img_url = img_val
            # 使用代理配置加载图片
            if img_url and (HTTP_PROXY or HTTPS_PROXY):
                proxies = {
                    "http": HTTP_PROXY,
                    "https": HTTPS_PROXY
                }
                try:
                    response = requests.get(img_url, proxies=proxies, timeout=10)
                    if response.status_code == 200:
                        img_url = response.url  # 确保使用代理后的 URL
                except Exception:
                    pass
            details.append({
                "category": COLLECTION_LABELS.get(name, name),
                "date": doc.get("date", ""),
                "number": doc.get("number", ""),
                "title": doc.get("title", ""),
                "magnet": doc.get("magnet", ""),
                "img": img_url,
                "_id": str(doc.get("_id", "")),
            })
        details.sort(key=lambda x: x["date"], reverse=True)
        result.append({"name": name, "label": COLLECTION_LABELS.get(name, name), "count": count, "details": details})
        total += count
    result.sort(key=lambda x: x["count"], reverse=True)
    return result, total

def search_by_keyword(cursor, keyword, collections):
    """
    在所有可用集合（表）中按标题或番号进行模糊搜索，返回与 get_today_stats 相同结构的数据。
    """
    result = []
    total = 0
    if not keyword:
        return [], 0
    kw = f"%{keyword}%"
    for name in collections:
        try:
            cursor.execute(
                f"SELECT `date`, `number`, `title`, `magnet`, `img` FROM `{name}` WHERE `title` LIKE ? OR `number` LIKE ?",
                (kw, kw)
            )
            docs = cursor.fetchall()
        except sqlite3.Error as e:
            logger.error(f'搜索表 {name} 失败: {str(e)}')
            docs = []
        details = []
        for doc in docs:
            # 处理 img 字段为 URL
            img_val = doc.get("img", "") if isinstance(doc, dict) else ""
            if isinstance(img_val, list):
                img_url = img_val[0] if img_val else ""
            elif isinstance(img_val, str) and img_val.strip().startswith("["):
                try:
                    img_list = ast.literal_eval(img_val)
                    img_url = img_list[0] if isinstance(img_list, list) and img_list else ""
                except Exception:
                    img_url = img_val
            else:
                img_url = img_val
            details.append({
                "category": COLLECTION_LABELS.get(name, name),
                "date": doc.get("date", "") if isinstance(doc, dict) else "",
                "number": doc.get("number", "") if isinstance(doc, dict) else "",
                "title": doc.get("title", "") if isinstance(doc, dict) else "",
                "magnet": doc.get("magnet", "") if isinstance(doc, dict) else "",
                "img": img_url,
                "_id": ""
            })
        if details:
            details.sort(key=lambda x: x["date"], reverse=True)
            result.append({"name": name, "label": COLLECTION_LABELS.get(name, name), "count": len(details), "details": details})
            total += len(details)
    result.sort(key=lambda x: x["count"], reverse=True)
    return result, total


@with_lady_db_connection
def get_rank_data(cursor, key_type):
    today = datetime.datetime.now().date()
    cursor.execute(
        "SELECT content FROM cache WHERE KEY = ? AND DATE(create_time) = ?",
        (key_type, today)
    )
    data = cursor.fetchall()
    return data if data else []

@with_lady_db_connection
def get_series_details(cursor, series_numbers):
    if not series_numbers:
        return []
    details = []
    for code in series_numbers:
        cursor.execute(
            "SELECT code, title, banner, release_date FROM code WHERE TRIM(code) = TRIM(?)",
            (code.strip(),)
        )
        result = cursor.fetchone()
        if result:
            details.append({
                "code": result['code'],
                "title": result['title'],
                "banner": result['banner'],
                "release_date": result['release_date']
            })
    return details

# 通用榜单接口
@app.route('/api/rank', methods=['GET'])
def get_rank():
    rank_type = request.args.get('type', 'daily')  # 默认为 daily
    if rank_type not in ['daily', 'weekly', 'monthly']:
        return jsonify({"error": "Invalid rank type. Must be 'daily', 'weekly', or 'monthly'."}), 400

    try:
        rank_data = get_rank_data(rank_type)
        series_numbers = []
        for content in rank_data:
            if content and isinstance(content, dict) and 'content' in content and content['content']:
                series_numbers.extend(content['content'].split(','))
        details = get_series_details(series_numbers)
        return jsonify({"data": details if details else []})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 保留原有接口（可选）
@app.route('/api/daily', methods=['GET'])
def get_daily():
    daily_data = get_rank_data("daily")
    if not daily_data:
        return jsonify({"data": [], "message": "No data available for today."})
    series_numbers = []
    for row in daily_data:
        if row and 'content' in row.keys():
            if row['content']:
                series_numbers.extend(row['content'].split(','))
    details = get_series_details(series_numbers)
    return jsonify({"data": details if details else []})

@app.route('/api/weekly', methods=['GET'])
def get_weekly():
    weekly_data = get_rank_data("weekly")
    if not weekly_data:
        return jsonify({"data": [], "message": "No data available for this week."})
    series_numbers = []
    for row in weekly_data:
        if row and 'content' in row.keys():
            if row['content']:
                series_numbers.extend(row['content'].split(','))
    details = get_series_details(series_numbers)
    return jsonify({"data": details if details else []})

@app.route('/api/monthly', methods=['GET'])
def get_monthly():
    monthly_data = get_rank_data("monthly")
    if not monthly_data:
        return jsonify({"data": [], "message": "No data available for this month."})
    series_numbers = []
    for row in monthly_data:
        if row and 'content' in row.keys():
            if row['content']:
                series_numbers.extend(row['content'].split(','))
    details = get_series_details(series_numbers)
    return jsonify({"data": details if details else []})

def get_collections(cursor):
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    rows = cursor.fetchall()
    # 统一处理为列表格式
    all_tables = set()
    for row in rows:
        table_name = row[0] if len(row) > 0 else ''
        if table_name:
            all_tables.add(table_name)
    return [k for k in COLLECTION_LABELS if k in all_tables]

@app.route("/", methods=["GET", "POST"])
def index():
    try:
        with sqlite3.connect(QUERY_DATABASE_PATH) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            collections = get_collections(cursor)
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            selected_date = today
            selected_collections = collections if collections else []
            stats = []
            total = 0
            selected_date_end = today
            date_mode = 'single'
            show_result = False
            keyword = ""
            search_all = False

            if request.method == "POST":
                show_result = True
                keyword = request.form.get("keyword", "").strip()
                search_all = "search_all" in request.form
                date_mode = request.form.get("date_mode", "single")
                selected_date = request.form.get("date", today)
                selected_collections = request.form.getlist("collections")

                if search_all or keyword:
                    if not keyword:
                        stats, total = get_today_stats(cursor, selected_date, selected_collections)
                    else:
                        stats, total = search_by_keyword(cursor, keyword, collections)
                else:
                    if date_mode == "range":
                        selected_date = request.form.get("date_start", today)
                        selected_date_end = request.form.get("date_end", selected_date)
                        stats, total = get_today_stats(cursor, selected_date, selected_collections, selected_date_end)
                    else:
                        stats, total = get_today_stats(cursor, selected_date, selected_collections)
                logger.info(f'查询结果: stats={stats}, total={total}')

            return render_template("index.html",
                                collections=collections,
                                collection_labels=COLLECTION_LABELS,
                                selected_date=selected_date,
                                selected_collections=selected_collections,
                                stats=stats if stats else [],
                                total=total,
                                selected_date_end=selected_date_end,
                                date_mode=date_mode,
                                show_result=show_result,
                                keyword=keyword,
                                search_all=search_all)
    except Exception as e:
        logger.error(f'处理根路径请求时发生错误: {str(e)}')
        return "Internal Server Error", 500

@app.route('/test')
def test_frontend():
    """测试前端功能的页面"""
    return send_from_directory('.', 'test_frontend.html')

if __name__ == "__main__":
    # 确保数据目录存在
    os.makedirs(os.path.dirname(QUERY_DATABASE_PATH), exist_ok=True)

    # 启动应用
    app.run(host=RUN_HOST, port=RUN_PORT, debug=False)