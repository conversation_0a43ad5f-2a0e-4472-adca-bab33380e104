<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>影视数据查询</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 定义全局CSS变量 - 用于整个网站的颜色、阴影、渐变等样式 */
        :root {
            --sidebar-bg: #ffffff;
            --card-bg: #ffffff;
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-bg: #f8fafc;
            --border-color: #e2e8f0;
            --hover-bg: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
        }

        /* 全局样式重置 - 确保所有元素使用统一的盒模型 */
        * {
            box-sizing: border-box;
        }

        /* 页面主体样式 - 设置页面背景、字体、颜色等全局属性 */
        body {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 卡片基础样式 - 定义通用卡片的外观、圆角和阴影 */
        .card {
            width: 100%;
            border-radius: 8px;
            box-shadow: var(--shadow-md);
            background: var(--card-bg);
        }



        /* 卡片标题样式 - 处理标题溢出和文本截断 */
        .card-title {
            font-size: 0.9rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 侧边栏样式 - 设置筛选区域的背景、圆角、阴影和定位 */
        .sidebar {
            background: var(--sidebar-bg);
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            margin-right: 0;
            padding: 16px 0;
            font-size: 0.9rem;
            position: sticky;
            top: 20px;
            height: fit-content;
            overflow: visible;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            max-width: 280px;
            z-index: 20001; /* 提高侧栏层级，确保不被任何结果区/提示层覆盖 */
        }

        /* 侧边栏悬停效果 - 添加鼠标悬停时的视觉反馈 */
        .sidebar:hover {
            box-shadow: var(--shadow-lg), 0 0 0 1px var(--primary-color);
        }

        /* 侧边栏表单卡片样式 - 设置筛选表单卡片的外观 */
        .sidebar-sticky form > .card {
            background: var(--secondary-bg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            margin-bottom: 8px;
            padding: 12px;
            font-size: 0.9rem;
            border-radius: 12px;
            transition: all 0.2s ease;
        }

        /* 侧边栏卡片悬停效果 - 添加鼠标悬停时的动画效果 */
        .sidebar-sticky form > .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 侧边栏最后一个卡片样式 - 移除底部间距 */
        .sidebar-sticky form > .card:last-child {
            margin-bottom: 0;
        }

        /* 进一步压缩侧边栏内容间距 - 优化空间利用 */
        .sidebar .section-title {
            margin-bottom: 8px;
            font-size: 1rem;
        }

        /* 侧边栏表单复选框样式 - 设置复选框的外观和间距 */
        .sidebar .form-check {
            margin-bottom: 6px;
            padding: 2px 6px;
        }

        /* 侧边栏表单间距调整 - 优化表单元素的间距 */
        .sidebar .mb-3 {
            margin-bottom: 12px !important;
        }

        /* 响应式设计：大屏幕 - 处理不同屏幕尺寸下的布局 */
        /* 响应式设计：中等屏幕 - 调整侧边栏宽度 */
        @media (max-width: 1199px) {
            .sidebar {
                max-width: 100%;
            }
        }

        @media (max-width: 991px) {
            .sidebar {
                margin-right: 0;
                border-radius: 16px;
                box-shadow: var(--shadow-md);
                position: relative;
                margin-bottom: 16px;
                padding: 12px 0;
                overflow: visible;
                max-width: 100%;
            }

            .sidebar-sticky form > .card {
                padding: 10px;
                margin-bottom: 6px;
            }
        }

        .section-title {
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 12px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            position: relative;
        }

        .section-title::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 20px;
            background: var(--gradient-primary);
            border-radius: 3px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
            pointer-events: none;
        }

        .section-title::after {
            content: "";
            position: absolute;
            bottom: -4px;
            left: 16px;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
            border-radius: 1px;
            pointer-events: none;
        }

        .form-check-label, .form-check-input {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .form-check-input {
            border-radius: 6px;
            border: 2px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-check-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-check {
            margin-bottom: 8px;
            padding: 4px 8px;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .form-check:hover {
            background-color: var(--hover-bg);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-left: 4px;
        }

        .row.g-1 > .col-6 { margin-bottom: 4px; }
        /* 网格化的筛选复选框 */
        .filter-grid { display: grid; grid-template-columns: repeat(2, minmax(0, 1fr)); column-gap: 6px; row-gap: 4px; justify-items: center; }
        .filter-item { min-width: 0; }
        .filter-item .form-check { margin-bottom: 0; }
        .filter-item .form-check-input { margin-top: 0.2rem; }

        .highlight-green {
            background-color: #4CAF50;
            color: white;
            border: 1px solid #4CAF50;
        }

        .sidebar .btn {
            border-radius: 12px;
        }

        .btn-emby-exist {
            background-color: #4CAF50 !important;
            border-color: #4CAF50 !important;
        }

        .row-emby-exist {
            background-color: rgba(76, 175, 80, 0.1) !important;
        }

        .row-emby-exist:hover {
            background-color: rgba(76, 175, 80, 0.2) !important;
        }

        .sidebar .btn-dark {
            background: var(--gradient-primary);
            border: none;
            font-size: 1.1rem;
            box-shadow: var(--shadow-md);
            color: white;
        }

        .sidebar .btn-dark:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .sidebar .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
            padding: 6px 16px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .sidebar .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .sidebar .btn-outline-secondary {
            border-color: var(--text-secondary);
            color: var(--text-secondary);
            padding: 6px 16px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .sidebar .btn-outline-secondary:hover {
            background: var(--text-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .sidebar .text-muted {
            color: var(--text-secondary) !important;
            font-size: 0.85rem;
            font-style: italic;
        }

        .sidebar input[type="date"], .sidebar input[type="text"] {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 6px 8px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            background: white;
            box-shadow: var(--shadow-sm);
            width: 100%;
            max-width: 100%;
        }

        .sidebar input[type="date"]:focus, .sidebar input[type="text"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .sidebar .mb-3 {
            margin-bottom: 20px !important;
        }

        /* 日期输入框样式优化 */
        .date-inputs-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .date-input-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-separator {
            color: var(--text-secondary);
            font-weight: 600;
            padding: 0 4px;
        }

        /* 右侧结果区美化 */
        main .card {
            border-radius: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            width: 100%;
            overflow: visible;
        }

        main .card .accordion {
            width: 100%;
            overflow: visible;
        }

        .accordion-item {
            border-radius: 16px !important;
            margin-bottom: 16px;
            border: 1px solid var(--border-color) !important;
            overflow: visible;
            background: var(--card-bg);
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .accordion-item:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .accordion-header {
            background: var(--secondary-bg);
            border-radius: 16px 16px 0 0;
        }

        .accordion-button {
            border-radius: 16px 16px 0 0;
            font-weight: 600;
            color: var(--text-primary);
            padding: 16px 20px;
            border: none;
            background: var(--secondary-bg);
            transition: all 0.3s ease;
        }

        .accordion-button:not(.collapsed) {
            background: var(--primary-color);
            color: white;
            box-shadow: none;
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .accordion-body {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 0 0 16px 16px;
            overflow: visible;
        }

        .badge {
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            padding: 6px 14px;
            margin-left: 12px;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 修复表格溢出和内容对齐 */
        .table-responsive {
            width: 100%;
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
        }

        table {
            width: 100%;
            border-radius: 12px;
            table-layout: fixed;
            border-collapse: separate;
            border-spacing: 0;
        }

        /* 临时调试：给所有元素添加边框来识别溢出元素 */
        /* * {
            outline: 1px solid red !important;
        } */

        /* 强制防止水平溢出 */
        html, body {
            overflow-x: hidden !important;
            max-width: 100vw !important;
            width: 100% !important;
        }


        main {

            min-width: 0;
        }



        /* 特别处理可能导致溢出的元素 */


        /* 确保所有列都不会溢出 */

        th, td {
            vertical-align: middle;
            padding: 12px 16px;
        }

        /* 允许表格内容适当换行 */
        th {
            white-space: nowrap;
        }

        td {
            white-space: normal;
            word-wrap: break-word;
        }

        /* 文本截断样式 */
        .text-truncate {
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
        }

        /* 特定列的宽度控制 - 使用百分比和固定宽度混合 */
        .table th:nth-child(1), .table td:nth-child(1) { /* 分类 */
            width: 12%;
            min-width: 70px;
            white-space: nowrap;
        }

        .table th:nth-child(2), .table td:nth-child(2) { /* 日期 */
            width: 16%;
            min-width: 100px;
            white-space: nowrap;
        }

        .table th:nth-child(3), .table td:nth-child(3) { /* 番号 */
            width: 16%;
            min-width: 100px;
            white-space: nowrap;
        }

        .table th:nth-child(4), .table td:nth-child(4) { /* 标题 */
            width: 42%;
            min-width: 180px;
        }

        .table th:nth-child(5), .table td:nth-child(5) { /* 操作 */
            width: 14%;
            min-width: 80px;
            white-space: nowrap;
        }

        .table th {
            font-weight: 700;
            color: var(--text-primary);
            background: var(--secondary-bg);
            border: none;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            background: white;
            border: none;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr:hover {
            background: var(--hover-bg);
            transition: all 0.2s ease;
        }

        .table-bordered th, .table-bordered td {
            border: 1px solid var(--border-color);
        }

        .title-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
            border-radius: 6px;
            padding: 8px 12px !important;
        }

        .title-cell:hover {
            background: var(--hover-bg);
            color: var(--primary-color);
            font-weight: 600;
        }


        /* 查询按钮美化 */
        .query-btn {
            background: var(--gradient-primary);
            border: none;
            font-weight: 700;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            border-radius: 16px;
            padding: 14px 28px;
            font-size: 1.1rem;
            text-transform: uppercase;
            position: relative;
            overflow: hidden;
        }

        .query-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
            pointer-events: none;
        }

        .query-btn:hover::before {
            left: 100%;
        }

        .query-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(37, 99, 235, 0.4);
        }

        .query-btn:active {
            transform: translateY(-1px);
        }

        /* 结果统计 */
        .result-summary {
            background: var(--gradient-primary);
            border-radius: 20px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-lg);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .result-summary::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
            pointer-events: none;
        }

        .result-summary .text-primary {
            color: white !important;
            font-weight: 800;
            font-size: 1.5rem;
        }

        /* 图片预览优化 */
        #image-preview {
            position: fixed;
            display: none;
            z-index: 30000;
            pointer-events: none;
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            padding: 8px;
            border: 2px solid var(--primary-color);
            transform: translate(-50%, -100%);
        }

        #image-preview img {
            max-width: 300px;
            max-height: 300px;
            border-radius: 8px;
            display: block;
            transition: all 0.2s ease;
        }

        /* 复制按钮优化 */
        .copy-btn {
            transition: all 0.3s ease;
            border-radius: 10px;
            font-weight: 600;
            padding: 6px 14px;
            font-size: 0.85rem;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: white;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }

        /* 全选/清空按钮组 */
        .selection-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .selection-controls .btn {
            flex: 1;
            font-weight: 600;
            border-radius: 10px;
            padding: 8px 16px;
            transition: all 0.3s ease;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state svg {
            opacity: 0.6;
            margin-bottom: 20px;
        }

        .empty-state h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 12px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container-fluid {
                padding-left: 12px;
                padding-right: 12px;
            }

            .sidebar {
                max-width: 100%;
                margin-bottom: 16px;
                padding: 12px 0;
            }

            .sidebar-sticky form > .card {
                padding: 10px;
                margin-bottom: 6px;
            }

            .section-title {
                font-size: 1rem;
                margin-bottom: 6px;
            }

            .section-title::before {
                height: 14px;
                width: 3px;
            }

            #image-preview img {
                max-width: 250px;
                max-height: 250px;
            }

            .query-btn {
                padding: 10px 20px;
                font-size: 0.95rem;
            }

            .result-summary {
                padding: 16px 20px;
            }

            .accordion-body {
                padding: 16px;
            }

            th, td {
                padding: 6px 8px;
                font-size: 0.8rem;
            }

            main .card {
                width: 100%;
                overflow: visible;
            }
        }

        @media (max-width: 576px) {
            .sidebar {
                padding: 10px 0;
            }

            .sidebar input[type="text"] {
                min-width: 100%;
                padding: 8px 12px;
            }

            .sidebar input[type="date"] {
                min-width: 100%;
                padding: 8px 12px;
            }

            .date-input-wrapper {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }

            .selection-controls {
                flex-direction: column;
                gap: 6px;
            }

            .sidebar-sticky form > .card {
                padding: 8px;
                margin-bottom: 4px;
            }

            .sidebar .form-check {
                margin-bottom: 4px;
                padding: 1px 4px;
            }

            th, td {
                padding: 4px 6px;
                font-size: 0.75rem;
            }

            .accordion-body {
                padding: 12px;
            }
        }
        @media (min-width: 768px) {
            .row.g-0 {
                flex-wrap: nowrap;
            }
        }
        .category-card .selection-controls { justify-content: center; margin-bottom: 10px; }
        .category-card .selection-controls .btn { max-width: 120px; }
        .category-card .filter-grid { grid-template-columns: repeat(2, max-content); column-gap: 20px; justify-content: initial; justify-items: start; width: max-content; margin: 12px auto 0; row-gap: 8px; }

        .date-card { display: flex; flex-direction: column; }
        .date-card .date-inputs-container { width: 100%; }
        .date-card .date-input-wrapper { width: 100%; }
        .date-card input[type="date"] { width: 100% !important; flex: 1 1 auto; min-width: 0; }

        @media (max-width: 576px) {
            /* 小屏也保持内容自适应高度 */
        }
        /* 分类筛选复选框对齐优化 */
        .category-card .filter-grid { justify-items: stretch; }
        .category-card .filter-item { width: 100%; }
        .category-card .form-check { display: flex; align-items: center; gap: 10px; margin-bottom: 6px; }
        .category-card .form-check-input { margin-top: 0; flex: 0 0 auto; }
        .category-card .form-check-label { flex: 1 1 auto; text-align: left; margin-left: 2px; padding-left: 2px; }
        .category-card .filter-grid { grid-template-columns: repeat(2, max-content); justify-content: initial; justify-items: start; width: max-content; margin: 8px auto 0; }
        /* 范围日期行宽度与输入自适应 */
        .date-card #range_inputs .d-flex.align-items-center { display: grid; grid-template-columns: 60px 1fr; column-gap: 8px; width: 100%; }
        .date-card #range_inputs .d-flex.align-items-center .form-label { margin: 0; }
        .date-card #range_inputs .d-flex.align-items-center input[type="date"] { width: 100% !important; }
        /* 让日期输入随容器伸缩，取消最小内容宽度限制 */
        .date-card input[type="date"] { width: 100% !important; inline-size: 100% !important; min-width: 0 !important; min-inline-size: 0 !important; box-sizing: border-box; }
        .date-card #range_inputs .d-flex.align-items-center input[type="date"] { width: 100% !important; inline-size: 100% !important; min-width: 0 !important; min-inline-size: 0 !important; }

        /* 榜单卡片样式 */
        .series-list {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px;
        }

        .series-item {
            width: 100%;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            height: 320px; /* 固定卡片高度 */
            overflow: hidden; /* 超出隐藏，保持统一高度 */
            position: relative; /* 供右上角徽标定位 */
        }
        .status-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #6c757d;
            color: #fff;
            border-radius: 10px;
            font-size: 0.75rem;
            padding: 2px 6px;
        }
        .status-badge.exist { background: #28a745; }
        .code-exist { background: rgba(76,175,80,0.15); border-radius: 4px; padding: 0 4px; }

        .copy-code { cursor: pointer; border-bottom: 1px dashed #aaa; }
        .copy-code:hover { color: #0d6efd; border-bottom-color: #0d6efd; }

        .series-item:hover {
            transform: scale(1.02);
        }

        .series-item img {
            width: 100%;
            height: 220px; /* 固定海报显示高度 */
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .series-item h3 {
            margin: 6px 0;
            color: #333;
            font-size: 0.9rem; /* 标题变小 */
            white-space: nowrap; /* 单行显示 */
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 省略号 */
        }

        .series-item p {
            margin: 2px 0; /* 收紧间距，配合固定高度 */
            color: #666;
            font-size: 0.85rem;
        }

        /* 选项卡样式 */
        .nav-tabs .nav-link {
            font-weight: 600;
            color: var(--text-secondary);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-bottom: 3px solid var(--primary-color);
        }

        .tab-content {
            padding: 20px 0;
        }
        /* 更小尺寸按钮 */
        .btn-xxs { padding: 0.1rem 0.35rem; font-size: 0.8rem; line-height: 1; border-radius: 0.2rem; }
        .copy-btn.btn-xxs, .copy-num-btn.btn-xxs { vertical-align: baseline; }

    </style>
</head>
<body data-show-result="{{ 1 if show_result else 0 }}">
<div class="container-fluid py-3">
    <div class="row g-0">
        <!-- 左侧导航栏筛选 -->
        <nav class="col-12 col-md-4 col-lg-3 col-xl-2 d-md-block sidebar p-0">
            <div class="sidebar-sticky pt-2">
                <div class="px-3 pb-2 mb-3 border-bottom" style="font-weight:700;font-size:1.1rem;letter-spacing:0.5px;color:var(--primary-color);text-align:center;">
                    🎬 影视筛选
                </div>
                <div class="sidebar-content">
                    <form method="post" class="text-start px-2" action="/">

                    <!-- 分类筛选（多选） -->
                    <div class="card p-2 mb-3 category-card">
                        <div class="section-title mb-2">分类筛选</div>
                        <div class="selection-controls">
                            <button type="button" class="btn btn-xs btn-outline-primary" id="selectAllBtn" style="font-size:0.8rem;padding:4px 6px;flex:1;">全选</button>
                            <button type="button" class="btn btn-xs btn-outline-secondary" id="clearAllBtn" style="font-size:0.8rem;padding:4px 6px;flex:1;">清空</button>
                        </div>
                        <div class="filter-grid">
                            {% for c in collections %}
                            <div class="filter-item">
                                <div class="form-check mb-1">
                                    <input class="form-check-input" type="checkbox" name="collections" value="{{ c }}" id="col-{{ c }}" {% if c in selected_collections %}checked{% endif %}>
                                    <label class="form-check-label" for="col-{{ c }}" style="font-size:0.85rem;">{{ collection_labels[c] }}</label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 日期筛选 -->
                    <div class="card p-2 mb-3 date-card">
                        <div class="section-title mb-2">日期筛选</div>
                        <div class="d-flex flex-column" style="gap:8px;">
                            <div class="d-flex justify-content-center" style="gap:16px;">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="date_mode" id="mode_single" value="single" checked>
                                    <label class="form-check-label" for="mode_single" style="font-size:0.9rem;">单日</label>
                                </div>
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="date_mode" id="mode_range" value="range">
                                    <label class="form-check-label" for="mode_range" style="font-size:0.9rem;">日期范围</label>
                                </div>
                            </div>
                            <div class="date-inputs-container w-100">
                                <div class="date-input-wrapper">
                                    <input type="date" class="form-control form-control-sm" name="date" value="{{ selected_date }}" id="date_single" style="font-size:0.85rem;">
                                </div>
                                <div class="date-input-wrapper w-100" id="range_inputs" style="display:none;">
                                    <div class="d-flex flex-column" style="gap:6px;">
                                        <div class="d-flex align-items-center" style="gap:8px;">
                                            <label class="form-label mb-0" style="font-size:0.8rem;color:var(--text-secondary);min-width:60px;">起始日期</label>
                                            <input type="date" class="form-control form-control-sm flex-grow-1" name="date_start" id="date_start" value="{{ selected_date }}" style="font-size:0.85rem;">
                                        </div>
                                        <div class="d-flex align-items-center" style="gap:8px;">
                                            <label class="form-label mb-0" style="font-size:0.8rem;color:var(--text-secondary);min-width:60px;">截止日期</label>
                                            <input type="date" class="form-control form-control-sm flex-grow-1" name="date_end" id="date_end" value="{{ selected_date_end }}" style="font-size:0.85rem;">
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="date_mode_val" value="{{ date_mode }}">
                            </div>
                        </div>
                    </div>

                    <!-- 内容或番号查询 -->
                    <div class="card p-3 mb-3">
                        <div class="section-title mb-2">内容或番号查询</div>
                        <div class="d-flex align-items-center">
                            <input type="text" class="form-control form-control-sm" name="keyword" placeholder="关键词或番号" value="{{ keyword }}" style="font-size:0.9rem;">
                        </div>
                        <div class="text-muted mt-1" style="font-size:0.8rem;">默认全库搜索</div>
                    </div>

                    <!-- 查询按钮单独一行 -->
                    <div class="mb-2 mt-3">
                        <button class="btn btn-dark w-100 py-2 query-btn" type="submit" style="font-size:1rem;">查询</button>
                    </div>
                    </form>
                </div>
            </div>
        </nav>

        <!-- 右侧结果栏 -->
        <main class="col-12 col-md-8 col-lg-9 col-xl-10 px-0 px-md-1" style="position: relative; z-index: 1;">
            <div class="card p-4 mt-2 mt-md-0">
                <div class="section-title mb-3">数据展示</div>

                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" id="dataTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="rank-tab" data-bs-toggle="tab" data-bs-target="#rank" type="button" role="tab" aria-controls="rank" aria-selected="true">榜单</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="result-tab" data-bs-toggle="tab" data-bs-target="#result" type="button" role="tab" aria-controls="result" aria-selected="false">查询结果</button>
                    </li>
                </ul>

                <!-- 选项卡内容 -->
                <div class="tab-content" id="dataTabContent">
                    <!-- 榜单内容 -->
                    <div class="tab-pane fade show active" id="rank" role="tabpanel" aria-labelledby="rank-tab">
                        <div class="d-flex justify-content-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="fetchAndDisplayData('daily', this)">日榜</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="fetchAndDisplayData('weekly', this)">周榜</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="fetchAndDisplayData('monthly', this)">月榜</button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mb-2" id="rank-toolbar">
                            <button type="button" id="rank-copy-all-btn" class="btn btn-sm btn-outline-primary" style="display:none;">一键推送未入库到115</button>
                        </div>

                        <div class="series-list" id="series-container">
                            <!-- 动态生成榜单卡片 -->
                        </div>
                    </div>

                    <!-- 查询结果内容 -->
                    <div class="tab-pane fade" id="result" role="tabpanel" aria-labelledby="result-tab">
                        {% if show_result %}
                        <div class="result-summary">
                            <div class="d-flex align-items-center justify-content-between">
                                <span class="fs-5">共找到 <strong class="text-primary">{{ total }}</strong> 条结果</span>
                                {% if total > 0 %}
                                <button class="btn btn-sm btn-outline-primary copy-all-magnets-btn" type="button">
                                    <i class="bi bi-magnet"></i> 一键复制所有磁力链接
                                </button>
                                <button class="btn btn-sm btn-outline-primary ms-2 push-all-magnets-btn" type="button" title="将全部磁力推送到115">一键离线全部</button>
                                {% endif %}
                            </div>
                        </div>

                        <div class="accordion mt-3" id="resultAccordion">
                            {% set visible_index = namespace(value=0) %}
                            {% for stat in stats %}
                            {% if stat.count > 0 %}
                            {% set visible_index.value = visible_index.value + 1 %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading-{{ visible_index.value }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse-{{ visible_index.value }}">
                                        {{ stat.label }} <span class="badge bg-secondary ms-2">{{ stat.count }}</span>
                                    </button>
                                </h2>
                                <div id="collapse-{{ visible_index.value }}" class="accordion-collapse collapse"
                                     data-bs-parent="#resultAccordion">
                                    <div class="accordion-body">
                                        {% if stat.details and stat.details|length > 0 %}
                                        <div class="mb-2">
                                            <button class="btn btn-sm btn-outline-primary copy-all-btn" type="button" data-idx="{{ visible_index.value }}">复制全部磁力链接</button>
                                            <button class="btn btn-sm btn-outline-primary ms-2 push-section-btn" type="button" data-idx="{{ visible_index.value }}">离线全部</button>

                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm align-middle">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>分类</th>
                                                        <th>日期</th>
                                                        <th>番号</th>
                                                        <th>标题</th>
                                                        <th>磁力链接</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="magnet-tbody-{{ visible_index.value }}">
                                                    {% for row in stat.details %}
                                                    <tr>
                                                        <td class="text-truncate">{{ row.category }}</td>
                                                        <td class="text-truncate">{{ row.date }}</td>
                                                        <td class="text-truncate" title="{{ row.number }}">{{ row.number }}</td>
                                                        <td class="title-cell text-truncate" data-img="{{ row.img }}">{{ row.title }}</td>
                                                        <td>
                                                            {% if row.magnet %}
                                                            <button class="btn btn-outline-secondary btn-sm btn-xxs copy-btn" title="复制磁力链接" onclick="copyMagnet('{{ row.magnet|e }}')">复制</button>
                                                            <button class="btn btn-outline-secondary btn-sm btn-xxs copy-btn" title="推送到115" onclick="push115Single('{{ row.magnet|e }}')">离线</button>

                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                        {% else %}
                                        <div class="text-muted text-center py-4">暂无数据</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="empty-state">
                            <div class="mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" class="bi bi-film" viewBox="0 0 16 16">
                                    <path d="M0 1a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V1zm4 0v6h8V1H4zm8 8H4v6h8V9zM1 1v2h2V1H1zm2 3H1v2h2V4zM1 7v2h2V7H1zm2 3H1v2h2v-2zm-2 3v2h2v-2H1zM15 1h-2v2h2V1zm-2 3v2h2V4h-2zm2 3h-2v2h2V7zm-2 3v2h2v-2h-2zm2 3h-2v2h2v-2z"/>
                                </svg>
                            </div>
                            <h5>🔍 开始您的影视数据探索之旅</h5>
                            <p class="mb-0">在左侧筛选器中选择您感兴趣的条件，然后点击查询按钮获取精彩内容</p>
                            <div class="mt-4">

                                <small class="text-muted">💡 提示：可以按分类、日期或关键词进行搜索</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<div id="image-preview">
    <img src="" alt="Preview">
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 让所有日期输入框点击时自动弹出日期选择器
    let rankMagnets = new Set();
let rank115Magnets = new Set();
const codeToMag = new Map();
const codeEmby = new Map();


['date_single','date_start','date_end'].forEach(function(id){
    var el = document.getElementById(id);
    if(el) {
        el.addEventListener('focus', function(){
            if(this.showPicker) this.showPicker();
        });
        el.addEventListener('click', function(){
            if(this.showPicker) this.showPicker();
        });
    }
});

// 日期筛选模式切换
document.getElementById('mode_single').onchange = function() {
    if(this.checked) {
        document.getElementById('date_single').parentElement.style.display = 'flex';
        document.getElementById('range_inputs').style.display = 'none';
    }
};

document.getElementById('mode_range').onchange = function() {
    if(this.checked) {
        document.getElementById('date_single').parentElement.style.display = 'none';
        document.getElementById('range_inputs').style.display = 'flex';
    }
};

// 页面加载时根据后端回显自动切换模式
window.onload = function() {
    // 添加页面加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.6s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);

    var date_mode = document.getElementById('date_mode_val') ? document.getElementById('date_mode_val').value : 'single';
    if (date_mode === 'range') {
        document.getElementById('mode_range').checked = true;
        document.getElementById('date_single').parentElement.style.display = 'none';
        document.getElementById('range_inputs').style.display = 'flex';
    } else {
        document.getElementById('mode_single').checked = true;
        document.getElementById('date_single').parentElement.style.display = 'flex';
        document.getElementById('range_inputs').style.display = 'none';
    }

    // 为表单添加提交动画
    const form = document.querySelector('form');
    const queryBtn = document.querySelector('.query-btn');

    if (form && queryBtn) {
        form.addEventListener('submit', function(e) {
            queryBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>查询中...';
            queryBtn.disabled = true;
        });
    }

    // 为卡片添加进入动画
    const cards = document.querySelectorAll('.sidebar .card, .accordion-item');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.4s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 + index * 100);
    });

    // 事件委托：复制全部磁力链接
    document.addEventListener('click', function(e) {
        const btn = e.target.closest('.copy-all-btn');
        if (btn) {
            const idx = btn.getAttribute('data-idx');
            if (idx) copyAllMagnets(idx, btn);
        }
    });

    // 默认仅在未进行“查询内容”时加载榜单数据
    const showResult = document.body.getAttribute('data-show-result') === '1';
    if (!showResult) {
        fetchAndDisplayData('daily');
    }
};

function copyMagnet(magnet) {
    const btn = event.target;
    const originalText = btn.textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(magnet).then(function() {
            showCopySuccess(btn, originalText);
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopy(magnet, btn, originalText);
        });
    } else {
        fallbackCopy(magnet, btn, originalText);
    }
}

function copyNumberBtn(numberText) {
    const btn = event.target;
    const originalText = btn.textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(numberText).then(function() {
            showCopySuccess(btn, originalText);
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopy(numberText, btn, originalText);
        });
    } else {
        fallbackCopy(numberText, btn, originalText);
    }
}


// --- 115 推送通用函数与事件 ---
async function pushTo115(magnets, btn){
    if (!magnets || magnets.length === 0) { showToast('无磁力可推送', 'error'); return; }
    const original = btn ? btn.textContent : '';
    if (btn) { btn.disabled = true; btn.textContent = '正在推送...'; }
    try {
        const resp = await fetch('/api/push115', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ magnets: Array.from(new Set(magnets)) })
        });
        const res = await resp.json().catch(()=>({ ok:false, message:'解析失败'}));
        if (!resp.ok || !res || res.ok === false) {
            showToast((res && res.message) || '推送失败（服务未开启或配置缺失）', 'error');
        } else {
            showToast(`✓ 已推送 ${res.pushed||magnets.length} 条，失败 ${res.failed||0} 条`, 'success');
            if (btn) { btn.textContent = `✓ 已推送 ${res.pushed||magnets.length} 条`; }
        }
    } catch (e) {
        console.error(e);
        showToast('推送异常', 'error');
    } finally {
        if (btn) setTimeout(()=>{ btn.textContent = original; btn.disabled = false; }, 1800);
    }
}

function push115Single(magnet) {
    const btn = event && event.target ? event.target : null;
    return pushTo115([magnet], btn);
}

function collectSectionMagnets(idx){
    const rows = document.querySelectorAll(`#magnet-tbody-${idx} tr`);
    const magnets = [];
    rows.forEach(tr => {
        const btn = tr.querySelector('td:nth-child(5) button[onclick^="copyMagnet("]');
        if (btn) {
            const m = btn.getAttribute('onclick').match(/copyMagnet\('(.+)'\)/);
            if (m && m[1]) magnets.push(m[1]);
        }
    });
    return magnets;
}

function collectAllResultMagnets(){
    const buttons = document.querySelectorAll('#resultAccordion td:nth-child(5) button[onclick^="copyMagnet("]');
    const set = new Set();
    buttons.forEach(b => {
        const m = b.getAttribute('onclick').match(/copyMagnet\('(.+)'\)/);
        if (m && m[1]) set.add(m[1]);
    });
    return Array.from(set);
}

// 事件委托：分组/全部离线
document.addEventListener('click', function(e){
    const secBtn = e.target.closest && e.target.closest('.push-section-btn');
    if (secBtn) {
        const idx = secBtn.getAttribute('data-idx');
        if (idx) { const mags = collectSectionMagnets(idx); pushTo115(mags, secBtn); }
        return;
    }
    const allBtn = e.target.closest && e.target.closest('.push-all-magnets-btn');
    if (allBtn) {
        const mags = collectAllResultMagnets();
        pushTo115(mags, allBtn);
        return;
    }
});



function showCopySuccess(btn, originalText) {
    btn.textContent = '✓ 已复制';
    btn.classList.add('btn-success');
    btn.classList.remove('btn-outline-secondary');

    setTimeout(() => {
        btn.textContent = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-secondary');
    }, 2000);
}

function fallbackCopy(text, btn, originalText) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            if (btn) showCopySuccess(btn, originalText);
            else showToast('磁力链接已复制！', 'success');
        } else {


            showToast('复制失败，请手动复制！', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制！', 'error');
    }
    document.body.removeChild(textarea);
}

function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        ${type === 'success' ? 'background: #10b981;' : 'background: #ef4444;'}
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);


        }, 300);
    }, 3000);
}

document.getElementById('selectAllBtn').onclick = function() {
    document.querySelectorAll('input[name="collections"]').forEach(cb => cb.checked = true);
};

document.getElementById('clearAllBtn').onclick = function() {
    document.querySelectorAll('input[name="collections"]').forEach(cb => cb.checked = false);
};

function copyAllMagnets(idx, btnEl) {
    const btn = btnEl || event?.target;
    const originalText = btn ? btn.textContent : '';

    if (btn) {
        btn.textContent = '⏳ 复制中...';
        btn.disabled = true;
    }

    const rows = document.querySelectorAll(`#magnet-tbody-${idx} tr`);
    let magnets = [];
    rows.forEach(tr => {
        const tds = tr.querySelectorAll('td');
        let magnet = '';
        if (tds[4]) {
            const mBtn = tds[4].querySelector('button');
            if (mBtn && mBtn.getAttribute('onclick')) {
                const match = mBtn.getAttribute('onclick').match(/copyMagnet\('(.+)'\)/);
                if (match) magnet = match[1];
            }
        }
        if (magnet) magnets.push(magnet);
    });


function copyNumber(numberText, el) {
    // 记住按钮的原始文字，避免恢复失败
    if (el && el.dataset && !el.dataset.originalLabel) {
        el.dataset.originalLabel = (el.textContent || '').trim() || '番号';
    }
    const restore = () => {
        if (!el) return;
        const orig = (el.dataset && el.dataset.originalLabel) ? el.dataset.originalLabel : '番号';
        el.textContent = orig;
        el.classList.remove('text-success');
    };
    const onSuccess = () => {
        if (el) {
            el.textContent = '已复制';
            el.classList.add('text-success');
            setTimeout(restore, 1000);
        } else {
            showToast('番号已复制', 'success');
        }
    };
    const onFail = () => showToast('复制失败', 'error');

    // 优先使用安全上下文下的 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText && (window.isSecureContext === undefined || window.isSecureContext)) {
        navigator.clipboard.writeText(numberText).then(onSuccess).catch(() => {
            // 失败时降级到 execCommand
            try {
                const ta = document.createElement('textarea');
                ta.value = numberText;
                ta.setAttribute('readonly', '');
                ta.style.position = 'fixed';
                ta.style.top = '0';
                ta.style.left = '0';
                ta.style.width = '1px';
                ta.style.height = '1px';
                ta.style.opacity = '0';
                document.body.appendChild(ta);
                ta.focus();
                ta.select();
                const ok = document.execCommand('copy');
                document.body.removeChild(ta);
                if (ok) onSuccess(); else onFail();
            } catch (e) { onFail(); }
        });
    } else {
        try {
            const ta = document.createElement('textarea');
            ta.value = numberText;
            ta.setAttribute('readonly', '');
            ta.style.position = 'fixed';
            ta.style.top = '0';
            ta.style.left = '0';
            ta.style.width = '1px';
            ta.style.height = '1px';
            ta.style.opacity = '0';
            document.body.appendChild(ta);
            ta.focus();
            ta.select();
            const ok = document.execCommand('copy');
            document.body.removeChild(ta);
            if (ok) onSuccess(); else onFail();
        } catch (e) { onFail(); }
    }
}

// 确保全局可用，供内联 onclick 调用
window.copyNumber = copyNumber;


    const finish = (ok, count) => {
        if (!btn) return;
        if (ok) {
            btn.textContent = `✓ 已复制 ${count} 条`;
            btn.classList.add('btn-success');
            btn.classList.remove('btn-outline-primary');
            showToast(`已复制全部磁力链接！共 ${count} 条`, 'success');
            setTimeout(() => {
                btn.textContent = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
                btn.disabled = false;
            }, 3000);
        } else {
            btn.textContent = originalText;
            btn.disabled = false;
        }
    };

    if (magnets.length > 0) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(magnets.join('\n')).then(function() {
                finish(true, magnets.length);
            }).catch(function(err) {
                console.error('复制失败:', err);
                fallbackCopy(magnets.join('\n'), btn, originalText);
            });



        } else {
            fallbackCopy(magnets.join('\n'), btn, originalText);
        }
    } else {
        showToast('无磁力链接可复制', 'error');
        if (btn) {
            btn.textContent = originalText;
            btn.disabled = false;
        }
    }
}

// 标题悬停显示图片预览
const preview = document.getElementById('image-preview');
let isPreviewVisible = false;

document.addEventListener('mouseover', function(e) {
    if (e.target.classList.contains('title-cell')) {
        const imgUrl = e.target.getAttribute('data-img');
        if (imgUrl) {
            const img = preview.querySelector('img');
            img.src = imgUrl;
            preview.style.display = 'block';
            isPreviewVisible = true;

            // 立即设置初始位置
            updatePreviewPosition(e);
        }
    }
});

function updatePreviewPosition(e) {
    if (!isPreviewVisible) return;

    // 使用页面坐标而不是视口坐标，避免滚动影响
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;

    // 计算相对于页面的鼠标位置
    const pageX = e.clientX + scrollX;
    const pageY = e.clientY + scrollY;

    // 设置预览位置（在鼠标上方）
    let previewX = pageX;
    let previewY = pageY - 20;

    // 边界检测 - 使用视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const previewWidth = 320; // 预览框宽度
    const previewHeight = 320; // 预览框高度

    // 水平边界检测
    if (e.clientX + previewWidth/2 > viewportWidth - 10) {
        previewX = pageX - previewWidth + 10;
    } else if (e.clientX - previewWidth/2 < 10) {
        previewX = pageX - 10;
    } else {
        previewX = pageX - previewWidth/2;
    }

    // 垂直边界检测
    if (e.clientY - previewHeight < 10) {
        previewY = pageY + 20; // 显示在鼠标下方
    }

    preview.style.left = previewX + 'px';
    preview.style.top = previewY + 'px';
}

document.addEventListener('mousemove', function(e) {
    if (isPreviewVisible) {
        updatePreviewPosition(e);
    }
});

document.addEventListener('mouseout', function(e) {
    if (e.target.classList.contains('title-cell')) {
        preview.style.display = 'none';
        preview.querySelector('img').src = '';
        isPreviewVisible = false;
    }
});

// 检查番号是否在EMBY库中
function checkEmbyNumber(number, buttonId) {
    if (!number) return;

    fetch(`/api/check_emby_number?number=${number}`)
        .then(response => response.json())
        .then(data => {
            if (data.exist) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.classList.add('btn-emby-exist');
                    const row = button.closest('tr');
                    if (row) {
                        row.classList.add('row-emby-exist');
                    }
                }
            }
        })
        .catch(error => console.error('Error checking EMBY:', error));
}

// 动态调用接口并生成榜单卡片
async function fetchAndDisplayData(endpoint, buttonElement) {
    try {
        // 更新按钮状态
        if (buttonElement) {
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            buttonElement.classList.add('active');
        }

        const response = await fetch(`/api/${endpoint}`);
        const data = await response.json();
        const container = document.getElementById('series-container');
        container.innerHTML = '';

        // 检查数据格式并正确处理
        if (data && data.data && data.data.length > 0) {
            rankMagnets = new Set();
            rank115Magnets = new Set();
            codeToMag.clear();
            codeEmby.clear();
            const copyAllBtn = document.getElementById('rank-copy-all-btn');
            if (copyAllBtn) {
                copyAllBtn.style.display = 'none';
                copyAllBtn.textContent = '一键推送未入库到115';
                copyAllBtn.onclick = async function() {
                    const original = copyAllBtn.textContent;
                    if (!rank115Magnets || rank115Magnets.size === 0) {
                        copyAllBtn.textContent = '暂无可推送';
                        copyAllBtn.disabled = true;
                        setTimeout(()=>{ copyAllBtn.textContent = original; copyAllBtn.disabled = false; }, 1500);
                        return;
                    }
                    copyAllBtn.disabled = true;
                    copyAllBtn.textContent = '正在推送...';
                    try {
                        const resp = await fetch('/api/push115', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ magnets: Array.from(rank115Magnets) })
                        });
                        const res = await resp.json();
                        if (!resp.ok || !res.ok) {
                            showToast(res.message || '推送失败（服务未开启或配置缺失）', 'error');
                            copyAllBtn.textContent = original; copyAllBtn.disabled = false; return;
                        }
                        copyAllBtn.textContent = `✓ 已推送 ${res.pushed} 条，失败 ${res.failed} 条`;
                        setTimeout(()=>{ copyAllBtn.textContent = original; copyAllBtn.disabled = false; }, 1800);
                    } catch (e) {
                        showToast('推送异常', 'error');
                        copyAllBtn.textContent = original; copyAllBtn.disabled = false;
                    }
                };
            }
            data.data.forEach((item, idx) => {
                const seriesItem = document.createElement('div');
                const offlineId = `offline-${idx}`;

                seriesItem.className = 'series-item';
                // 处理可能缺失的字段
                const title = item.title || item.name || '无标题';
                const code = item.code || item.number || '无番号';
                const releaseDate = item.release_date || item.date || '未知日期';
                const banner = item.banner || item.img || 'https://via.placeholder.com/300x450';

                const badgeId = `badge-${idx}`;
                const codeId = `code-${idx}`;
                const copyId = `copy-${idx}`;

                seriesItem.innerHTML = `
                    <span class="status-badge" id="${badgeId}">检查中</span>
                    <img src="${banner}" alt="${title}">
                    <p>番号: <span id="${codeId}" class="code-text">${code}</span> <button type="button" class="btn btn-outline-secondary btn-sm btn-xxs ms-2 copy-num-btn" title="复制番号" data-code="${code}" onclick="copyNumberBtn(this.dataset.code)">番号</button> <button id="${copyId}" class="btn btn-outline-secondary btn-sm btn-xxs ms-2 d-none" title="复制磁力链接">磁力</button> <button id="${offlineId}" class="btn btn-outline-secondary btn-sm btn-xxs ms-2 d-none" title="推送到115">离线</button></p>
                    <p>发售日期: ${releaseDate}</p>
                    <h3>${title}</h3>
                `;
                container.appendChild(seriesItem);




                // Emby 模糊检查
                fetch(`/api/check_emby_number?number=${encodeURIComponent(code)}`)
                    .then(r => r.json())
                    .then(d => {
                        const badge = document.getElementById(badgeId);
                        if (!badge) return;
                        const exist = !!(d && d.exist);
                        codeEmby.set(code, exist);
                        if (exist) {
                            badge.textContent = '已入库';
                            badge.classList.add('exist');
                            const mag = codeToMag.get(code);
                            if (mag) rank115Magnets.delete(mag.trim());
                        } else {
                            badge.textContent = '未入库';
                            const mag = codeToMag.get(code);
                            if (mag) rank115Magnets.add(mag.trim());
                        }
                        if (copyAllBtn) copyAllBtn.style.display = rank115Magnets.size > 0 ? 'inline-block' : 'none';
                    }).catch(() => {});

                // MySQL 磁力检查
                fetch(`/api/check_number?number=${encodeURIComponent(code)}`)
                    .then(r => r.json())
                    .then(d => {
                        if (d && d.exist && d.magnet) {
                            const mag = (typeof d.magnet === 'string') ? d.magnet.trim() : '';
                            const codeEl = document.getElementById(codeId);
                            const copyBtn = document.getElementById(copyId);
                            const offlineBtn = document.getElementById(offlineId);

                            if (codeEl) codeEl.classList.add('code-exist');
                            if (copyBtn) {
                                copyBtn.classList.remove('d-none');
                            if (offlineBtn) {
                                offlineBtn.classList.remove('d-none');
                                offlineBtn.onclick = function(){ push115Single(mag); };
                            }

                                copyBtn.onclick = function(){ copyMagnet(mag); };
                            }
                            if (mag) {
                                rankMagnets.add(mag);
                                codeToMag.set(code, mag);
                                const exist = codeEmby.get(code);
                                if (exist === false) rank115Magnets.add(mag);
                            }
                            if (copyAllBtn) copyAllBtn.style.display = rank115Magnets.size > 0 ? 'inline-block' : 'none';
                        }
                    }).catch(() => {});
            });
        } else {
            container.innerHTML = '<p class="text-center w-100">暂无数据</p>';
        }
    } catch (error) {
        console.error(`Error fetching ${endpoint} data:`, error);
        document.getElementById('series-container').innerHTML = '<p class="text-center w-100">加载数据失败，请稍后重试</p>';
    }
}

// 如果是从查询结果页面刷新，自动切换到查询结果选项卡
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有查询结果
    const hasResults = document.querySelector('#result .result-summary') ||
                      document.querySelector('#result .accordion-item') ||
                      document.querySelector('#result .empty-state');

    if (hasResults && !document.querySelector('#result .empty-state')) {

        // 切换到查询结果选项卡
        const resultTab = document.querySelector('#result-tab');
        if (resultTab) {
            const tab = new bootstrap.Tab(resultTab);
            tab.show();
        }
    }
});
</script>
<script>
// 切换到“榜单”选项卡时，若容器为空则懒加载一次
(function(){
  const rankTabBtn = document.getElementById('rank-tab');
  if (!rankTabBtn) return;
  rankTabBtn.addEventListener('shown.bs.tab', function(){
    const container = document.getElementById('series-container');
    if (container && container.children.length === 0) {
      fetchAndDisplayData('daily');
    }
  });
})();
</script>
<script>
document.addEventListener('click', function(ev){
  var btn = ev.target && (ev.target.closest ? ev.target.closest('.copy-num-btn') : null);
  if (btn) {
    ev.preventDefault();
    var code = (btn.dataset && btn.dataset.code) ? btn.dataset.code : '';
    if (code) { try { copyNumber(code, btn); } catch(e){} }
    return;
  }
  var t = ev.target && (ev.target.closest ? ev.target.closest('.copy-code') : null);
  if (t) {
    ev.preventDefault();
    var text = (t.dataset && t.dataset.code) ? t.dataset.code : ((t.textContent||'').trim());
    if (text) { try { copyNumber(text, t); } catch(e){} }
  }
});
</script>


</body>
</html>