# 配置文件

# SQLite配置（Docker友好的路径配置）
import os

def _get_data_dir():
    """获取数据目录路径，优先使用环境变量，适配Docker部署"""
    # 1) 环境变量优先（Docker容器内使用）
    data_dir = os.getenv("DATA_DIR")

    if data_dir is None:
        # 2) 本地开发环境，使用项目目录下的data文件夹
        project_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(project_dir, "data")

    # 3) 确保数据目录存在
    os.makedirs(data_dir, exist_ok=True)

    return data_dir

def _get_database_path(db_name):
    """获取数据库文件路径"""
    data_dir = _get_data_dir()
    db_path = os.path.join(data_dir, db_name)

    # 确保数据库文件的目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    return db_path

# 数据库路径配置
QUERY_DATABASE_PATH = _get_database_path("sync_data.db")
LADY_DATABASE_PATH = _get_database_path("lady.db")

# EMBY配置
# Emby服务器地址样式
EMBY_SERVER_URL = "http://21618.top:7212/"
EMBY_API_KEY = "4b6450e46f4e439b93e9f591e993be66"
EMBY_LIBRARY_ID = "280229"

# 登录配置
LOGIN_USERNAME = "admin"
LOGIN_PASSWORD = "password123"


# Telegram配置
TG_BOT_TOKEN = "**********************************************"
TG_CHAT_ID = " "
# Telegram代理配置（如果需要代理，请设置PROXY_URL；如果不需要代理，请保持为空字符串）
# Telegram代理URL样式
TG_PROXY_URL = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"

# HTTP代理配置（如果需要代理，请设置HTTP_PROXY；如果不需要代理，请保持为空字符串）
HTTP_PROXY = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"
HTTPS_PROXY = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"

# 115 网盘配置
# 临时开关：是否推送磁力到 115 网盘（False=关闭，True=开启）
ENABLE_115_PUSH =  True
COOKIE_115 = "UID=80004131_H3_1758351905; CID=c755ac3ed41e23ee0194e063845c1073; SEID=052fed867f9c5d119f34ac5963b82ea7fe78ef22320030d86d95b1b1cff1a64b93f1030bbda7aec6fbd9b1aeb55242ae91343c5a7ec379838188c003; KID=99e6ed0090441d24818fd0aeb672fc95;"  # 例如: "UID=123; CID=456; other_key=value"
TARGET_DIR_115 = "3151053100959882948"  # 115 网盘的目标目录CID

# 运行地址配置（指定程序运行时使用的地址，而不是使用默认的群晖地址）
# 运行主机地址样式
RUN_HOST = "0.0.0.0"  # 留空表示使用默认地址，可以设置为特定IP地址如"*************"
RUN_PORT = 27020    # 0表示使用默认端口，可以设置为特定端口如5000

# 同步ID文件名样式
LAST_SYNC_ID_FILE = "last_sync_id.txt"

# 需要同步的字段
FIELDS = [
    '_id', 'title', 'post_time', 'img', 'magnet', 'number', 'date', 'tid'
]
# 数据库字段定义样式
FIELD_DEF = '''
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    _id VARCHAR(32) UNIQUE,
    title TEXT,
    post_time VARCHAR(32),
    img TEXT,
    magnet TEXT,
    number VARCHAR(32),
    date VARCHAR(32),
    tid VARCHAR(32)
'''