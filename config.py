# 配置文件

# SQLite配置（自动探测常见路径，环境变量优先）
import os

def _detect_db_path():
    # 1) 环境变量优先
    env = os.getenv("DATABASE_PATH")
    if env and os.path.exists(env):
        return env

    # 2) 常见路径候选（按优先级从高到低）
    candidates = [
        "/data/lady.db",  # 容器内标准挂载
        "/volume1/docker/byte-muse/data/lady.db",  # 群晖宿主路径（若原样挂进容器）
        "Z:\\docker\\byte-muse\\data\\lady.db",  # Windows 本地路径
        "\\\\NAS\\docker\\byte-muse\\data\\lady.db",  # Windows UNC 网络路径
        os.path.join(os.getcwd(), "lady.db"),  # 当前工作目录
        os.path.join(os.path.dirname(__file__), "lady.db"),  # 与本文件同目录
    ]
    for p in candidates:
        try:
            if os.path.exists(p):
                return p
        except Exception:
            pass

    # 3) 兜底：返回环境变量即使不存在；否则默认 /data/lady.db
    return env or "/data/lady.db"

QUERY_DATABASE_PATH = os.path.join(os.path.dirname(__file__), "data", "sync_data.db")
LADY_DATABASE_PATH = os.path.join(os.path.dirname(__file__), "data", "lady.db")

# EMBY配置
# Emby服务器地址样式
EMBY_SERVER_URL = "http://21618.top:7212/"
EMBY_API_KEY = "4b6450e46f4e439b93e9f591e993be66"
EMBY_LIBRARY_ID = "280229"

# 登录配置
LOGIN_USERNAME = "admin"
LOGIN_PASSWORD = "password123"


# Telegram配置
TG_BOT_TOKEN = "**********************************************"
TG_CHAT_ID = " "
# Telegram代理配置（如果需要代理，请设置PROXY_URL；如果不需要代理，请保持为空字符串）
# Telegram代理URL样式
TG_PROXY_URL = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"

# HTTP代理配置（如果需要代理，请设置HTTP_PROXY；如果不需要代理，请保持为空字符串）
HTTP_PROXY = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"
HTTPS_PROXY = "http://192.168.0.25:20171"  # 示例: "http://127.0.0.1:1080"

# 115 网盘配置
# 临时开关：是否推送磁力到 115 网盘（False=关闭，True=开启）
ENABLE_115_PUSH =  True
COOKIE_115 = "UID=80004131_H3_1758351905; CID=c755ac3ed41e23ee0194e063845c1073; SEID=052fed867f9c5d119f34ac5963b82ea7fe78ef22320030d86d95b1b1cff1a64b93f1030bbda7aec6fbd9b1aeb55242ae91343c5a7ec379838188c003; KID=99e6ed0090441d24818fd0aeb672fc95;"  # 例如: "UID=123; CID=456; other_key=value"
TARGET_DIR_115 = "3151053100959882948"  # 115 网盘的目标目录CID

# 运行地址配置（指定程序运行时使用的地址，而不是使用默认的群晖地址）
# 运行主机地址样式
RUN_HOST = "0.0.0.0"  # 留空表示使用默认地址，可以设置为特定IP地址如"*************"
RUN_PORT = 27020    # 0表示使用默认端口，可以设置为特定端口如5000

# 同步ID文件名样式
LAST_SYNC_ID_FILE = "last_sync_id.txt"

# 需要同步的字段
FIELDS = [
    '_id', 'title', 'post_time', 'img', 'magnet', 'number', 'date', 'tid'
]
# 数据库字段定义样式
FIELD_DEF = '''
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    _id VARCHAR(32) UNIQUE,
    title TEXT,
    post_time VARCHAR(32),
    img TEXT,
    magnet TEXT,
    number VARCHAR(32),
    date VARCHAR(32),
    tid VARCHAR(32)
'''