# 荤素搭配工程文档

## 工程结构

- **`app.py`**: 主应用文件，基于 Flask 框架实现 Web 服务，提供多个 API 接口。
- **`check_mongo_conn.py`**: 从 MongoDB 同步数据到 MySQL 数据库的脚本。
- **`config.py`**: 配置文件，包含数据库连接、API 密钥、代理设置等。
- **`requirements.txt`**: 依赖包列表。
- **`templates/`**: 存放前端模板文件（如果有）。

## 核心功能

### 1. `app.py`
- **Flask 应用**:
  - 提供以下 API 接口：
    - `/api/push115`: 推送磁力链接到 115 网盘。
    - `/api/query_by_category`: 按分类和日期查询数据。
    - `/api/check_number`: 检查番号是否存在。
    - `/api/check_emby_number`: 在 Emby 中检查番号是否已入库。
  - 支持 MySQL 和 SQLite 数据库，提供数据库连接装饰器（`with_mysql_connection` 和 `with_sqlite_connection`）。
  - 115 网盘推送功能：通过解析 Cookie 和路径实现磁力链接的推送。

### 2. `check_mongo_conn.py`
- **数据同步**:
  - 从 MongoDB 同步数据到 MySQL 数据库。
  - 支持增量同步，基于 `post_time` 字段判断是否需要同步新数据。
  - 同步完成后，通过 Telegram 机器人推送通知。

### 3. `config.py`
- **配置管理**:
  - 数据库连接信息（MongoDB、MySQL、SQLite）。
  - Emby 服务器配置（URL、API Key、Library ID）。
  - Telegram 机器人配置（Token、Chat ID、代理）。
  - 115 网盘配置（Cookie、目标目录 CID）。
  - HTTP 代理配置。

## 技术栈

- **后端**:
  - Flask: 轻量级 Web 框架。
  - MongoDB: 作为数据源。
  - MySQL/SQLite: 作为目标数据库。
  - Telegram Bot API: 用于推送通知。
- **工具**:
  - `pymongo`: MongoDB 客户端。
  - `mysql-connector`: MySQL 客户端。
  - `requests`: HTTP 请求库。
  - `sqlite3`: SQLite 客户端。

## 配置说明

### 1. 数据库配置
- **MongoDB**: 通过 `SRC_MONGO_URI` 和 `SRC_MONGO_DB` 配置连接信息。
- **MySQL**: 通过 `MYSQL_HOST`、`MYSQL_PORT`、`MYSQL_USER`、`MYSQL_PASSWORD` 和 `MYSQL_DB` 配置连接信息。
- **SQLite**: 通过 `DATABASE_PATH` 配置数据库文件路径。

### 2. 服务配置
- **Emby**: 通过 `EMBY_SERVER_URL`、`EMBY_API_KEY` 和 `EMBY_LIBRARY_ID` 配置。
- **Telegram**: 通过 `TG_BOT_TOKEN`、`TG_CHAT_ID` 和 `TG_PROXY_URL` 配置。
- **115 网盘**: 通过 `ENABLE_115_PUSH`、`COOKIE_115` 和 `TARGET_DIR_115` 配置。

### 3. 代理配置
- **HTTP/HTTPS 代理**: 通过 `HTTP_PROXY` 和 `HTTPS_PROXY` 配置。

## 使用说明

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **启动服务**:
   ```bash
   python app.py
   ```

3. **同步数据**:
   ```bash
   python check_mongo_conn.py
   ```

## 后续优化建议

- **日志记录**: 增加日志功能，便于调试和监控。
- **定时任务**: 实现定时自动同步数据。
- **扩展存储服务**: 支持更多云存储服务（如阿里云盘、百度网盘）。
- **用户认证**: 增加 JWT 认证功能。