version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./config.py:/app/config.py
      - ./data:/app/data
    environment:
      - DATABASE_PATH=/app/data/lady.db
    restart: unless-stopped

  sync:
    build: .
    command: python check_mongo_conn.py
    volumes:
      - ./config.py:/app/config.py
      - ./data:/app/data
    environment:
      - DATABASE_PATH=/app/data/lady.db
    restart: unless-stopped